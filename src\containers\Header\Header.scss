@import "../../styles/common.scss";
$navy-dark: #1a3a6c;
$navy-medium: #2c4272;
$navy-light: #3a5998;
$white: #ffffff;
$light-gray: #f7f9fc;
$medium-gray: #e4e8f0;
$dark-gray: #8492a6;
$accent-color: #ff5a5a;
$transition: all 0.3s ease;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
.header-container {
  z-index: 99;
  display: flex;
  justify-content: space-between;
  background-color: #1a3a6c;
  color: #ffffff;
  height: 50px;
  position: relative;

  .header-tabs-container {
    flex-grow: 0.05;
  }

  .header-right-content {
    display: flex;
    align-items: center;
    gap: 15px; // Space between user info and logout
    padding-right: 15px; // Padding on the right edge
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 8px; // Space between name and avatar
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    position: relative;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    // Thêm biểu tượng mũi tên xuống
    &::after {
      content: "\f107"; // Unicode cho icon mũi tên xuống của FontAwesome
      font-family: "Font Awesome 5 Free";
      font-weight: 900;
      margin-left: 5px;
      font-size: 14px;
      transition: transform 0.2s ease;
    }

    &.active::after {
      transform: rotate(180deg);
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      min-width: 12.5%;
      z-index: 1000;
      margin-top: 10px;
      overflow: hidden;
      transform-origin: top right;
      animation: dropdownFadeIn 0.25s ease;
      display: block; /* Đảm bảo dropdown luôn hiển thị khi được render */

      // Thêm mũi tên nhỏ ở phía trên dropdown
      &::before {
        content: "";
        position: absolute;
        top: -6px;
        right: 20px;
        width: 12px;
        height: 12px;
        background-color: #fff;
        transform: rotate(45deg);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        border-left: 1px solid rgba(0, 0, 0, 0.05);
      }

      .dropdown-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        color: #333;
        text-decoration: none;
        transition: all 0.2s;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        @keyframes dropdownFadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        &:hover {
          background-color: #f5f5f5;
          padding-left: 18px;
        }

        // Thêm icon cho các mục trong dropdown
        &::before {
          font-family: "Font Awesome 5 Free";
          font-weight: 900;
          margin-right: 10px;
          font-size: 14px;
          width: 20px;
          text-align: center;
        }

        &:nth-child(1)::before {
          content: "\f007"; // Icon user
          color: #3a5998;
        }

        &:nth-child(2)::before {
          content: "\f3c5"; // Icon location/address
          color: #4caf50;
        }
      }
    }

    .user-name {
      color: #ffffff;
      font-weight: 500;
    }

    .user-avatar {
      width: 30px;
      height: 30px;
      border-radius: 50%; // Make it circular
      border: 1px solid #ccc; // Optional border
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
      }
    }
  }

  .btn-logout {
    color: #ffffff;
    background-color: #2d5296;
    line-height: 50px;
    height: 50px;
    padding: 0 10px;
    &:hover {
      background-color: adjust-color($colormain, $lightness: -5);
      transition: background-color 0.3s ease;
    }
    i {
      font-size: $base-size + 1px;
    }
  }
}
.header2-cart-container {
  display: flex;
  align-items: center;
  justify-self: center;
  height: 100%;
}
.header2-cart {
  width: 30px;
  height: 30px;
  // position: relative;
  top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: $transition;

  &:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
  }
}
.header2-nav-cart-count {
  position: relative;
  right: 8px;
  bottom: 8px;
  background-color: $accent-color;
  color: $white;
  font-size: 12px;
  font-weight: bold;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  text-decoration: underline;
  animation: pulse 2s infinite;
}
.header-myorders {
  color: #ffffff;
  font-weight: 500;
  font-size: 15px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  &:hover {
    color: $accent-color;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }
}
.header-search-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 400%;
  max-width: 700px;
}

.header-searchImage {
  width: 20px;
  height: 20px;
  position: relative;
  right: 30px;
  // top: 50%;
  // transform: translateY(-50%);
  opacity: 0.7;
  transition: $transition;
  cursor: pointer;
  &:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
}

.header-searchBar {
  width: 100%;
  height: 38px;
  border: none;
  border-radius: $border-radius;
  outline: none;
  padding: 0 40px 0 15px;
  background-color: rgba(255, 255, 255, 0.8);
  transition: $transition;
  font-size: 14px;
  color: $navy-dark;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  &:focus {
    background-color: $white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  }

  &::placeholder {
    color: $dark-gray;
    opacity: 0.7;
  }
}
@media (max-width: 1400px) {
  .header-myorders {
    text-indent: -9999px;
    overflow: hidden;
    min-width: 50px;
    &::before {
      content: "";
      display: flex;
      width: 30px;
      height: 30px;
      background-image: url("../../assets/images/icons/order.png");
      background-size: contain; // hoặc cover, 100% 100%
      background-repeat: no-repeat;
      // bottom: 50%; // Thêm dòng này
      // transform: translate(100%, 100%); // Thêm dòng này
      font-family: "Font Awesome 5 Free";
      font-weight: 900;
      position: relative;
      // left: 50%;
      text-indent: 0;
      color: white;
    }
  }
}
@media (max-width: 800px) {
  // Variables chia đôi
  $border-radius: 4px; // 8px / 2
  $box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08); // 4px / 2, 12px / 2

  .header-container {
    height: 25px; // 50px / 2

    .header-right-content {
      gap: 7.5px; // 15px / 2
      padding-right: 7.5px; // 15px / 2
    }

    .user-info {
      gap: 4px; // 8px / 2
      padding: 2.5px 5px; // 5px / 2, 10px / 2
      border-radius: 2.5px; // 5px / 2

      &::after {
        margin-left: 2.5px; // 5px / 2
        font-size: 7px; // 14px / 2
      }

      .dropdown-menu {
        border-radius: 4px; // 8px / 2
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15); // 4px / 2, 20px / 2
        margin-top: 5px; // 10px / 2

        &::before {
          top: -3px; // -6px / 2
          right: 10px; // 20px / 2
          width: 6px; // 12px / 2
          height: 6px; // 12px / 2
          border-top: 0.5px solid rgba(0, 0, 0, 0.05); // 1px / 2
          border-left: 0.5px solid rgba(0, 0, 0, 0.05); // 1px / 2
        }

        .dropdown-item {
          padding: 6px 7.5px; // 12px / 2, 15px / 2
          border-bottom: 0.5px solid rgba(0, 0, 0, 0.05); // 1px / 2

          @keyframes dropdownFadeIn {
            from {
              transform: translateY(-5px) scale(0.95); // -10px / 2
            }
          }

          &:hover {
            padding-left: 9px; // 18px / 2
          }

          &::before {
            margin-right: 5px; // 10px / 2
            font-size: 7px; // 14px / 2
            width: 10px; // 20px / 2
          }
        }
      }

      .user-avatar {
        width: 15px; // 30px / 2
        height: 15px; // 30px / 2
        border: 0.5px solid #ccc; // 1px / 2

        &:hover {
          box-shadow: 0 0 2.5px rgba(255, 255, 255, 0.5); // 5px / 2
        }
      }
    }

    .btn-logout {
      line-height: 25px; // 50px / 2
      height: 25px; // 50px / 2
      padding: 0 5px; // 10px / 2
    }
  }

  .header2-cart {
    width: 15px; // 30px / 2
    height: 15px; // 30px / 2
    top: 7.5px; // 15px / 2

    &:hover {
      filter: drop-shadow(0 0 2.5px rgba(255, 255, 255, 0.5)); // 5px / 2
    }
  }

  .header2-nav-cart-count {
    right: 4px; // 8px / 2
    bottom: 4px; // 8px / 2
    font-size: 6px; // 12px / 2
    width: 10px; // 20px / 2
    height: 10px; // 20px / 2
    animation: pulse 1s infinite; // 2s / 2
  }

  .header-myorders {
    min-width: 30px;

    font-size: 7.5px; // 15px / 2
    padding: 0 5px; // 10px / 2
    border-radius: 2.5px; // 5px / 2
  }

  .header-searchImage {
    width: 10px; // 20px / 2
    height: 10px; // 20px / 2
    right: 15px; // 30px / 2
  }

  .header-searchBar {
    height: 19px; // 38px / 2
    border-radius: 4px; // 8px / 2 (from $border-radius)
    padding: 0 20px 0 7.5px; // 40px / 2, 15px / 2
    font-size: 7px; // 14px / 2
    box-shadow: 0 1px 2.5px rgba(0, 0, 0, 0.1); // 2px / 2, 5px / 2

    &:focus {
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15); // 4px / 2, 10px / 2
    }
  }

  // Media query cho responsive icons
  @media (max-width: 800px) {
    // 1400px / 2
    .header-myorders {
      &::before {
        width: 15px; // 30px / 2
        height: 15px; // 30px / 2
      }
    }
  }
}
