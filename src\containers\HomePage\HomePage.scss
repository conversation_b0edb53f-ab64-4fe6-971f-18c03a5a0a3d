// Variables - SỬA: C<PERSON>p nhật m<PERSON>u ch<PERSON>h theo yêu cầu
$primary-blue: #3a5998; // SỬA: <PERSON><PERSON><PERSON> ch<PERSON>h xanh navy
$success-green: #4caf50; // THÊM: <PERSON><PERSON><PERSON> xanh lá cho success
$danger-red: #e53935; // THÊM: <PERSON><PERSON><PERSON> đỏ cho error/danger
$navy-dark: #1a3a6c;
$navy-medium: #2c4272;
$navy-light: #3a5998;
$white: #ffffff;
$light-gray: #f7f9fc;
$medium-gray: #e4e8f0;
$dark-gray: #8492a6;
$accent-color: #ff5a5a;
$transition: all 0.3s ease;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
.homepage-productscontainer {
  margin: 0;
  padding: 40px 20px;
  max-height: 78vh;
  min-height: 78vh;
  flex-wrap: wrap;
  overflow-y: scroll;
  display: flex;
  gap: 30px;
  justify-content: center;
  background-color: #f8f9fa;
  // scrollbar-width: none;
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 40px;

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 6px solid #f3f3f3;
      border-top: 6px solid $navy-dark;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 30px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    p {
      color: $navy-dark;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .no-products {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 80px;

    i {
      font-size: 64px;
      color: $dark-gray;
      margin-bottom: 30px;
    }

    p {
      color: $dark-gray;
      font-size: 20px;
    }
  }

  // Make products larger
  .product-container {
    width: 280px;
    margin: 0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    // .product-image-container {
    //   height: 280px;

    //   .product-image {
    //     height: 100%;
    //     object-fit: cover;
    //   }
    // }

    .product-info-container {
      padding: 20px;

      .product-name {
        font-size: 18px;
        margin-bottom: 12px;
        max-height: 50px;
      }

      .product-shop {
        margin: 10px 0;
      }

      .product-price {
        margin: 15px 0;

        .original-price {
          font-size: 16px;
        }

        .sale-price {
          font-size: 20px;
        }
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.homepage-container {
  display: flex;
  flex-direction: column;
}
.homepage-dash {
  display: block;
  margin-top: 20px;
  max-height: 2vh;
  height: 100%;
  width: 50%;
  margin: 0px auto;
  background: $navy-medium;
  border: 0;
  z-index: 1;
}
.homepage-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2vh 0;
  gap: 10px;

  button {
    max-width: 5vh;
    width: 100%;
    height: 100%;
    max-height: 5vh;
    border-radius: 8px;
    border: 1px solid #e4e8f0;
    background-color: #ffffff;
    color: #1a3a6c;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f7f9fc;
      border-color: #3a5998;
      transform: translateY(-2px);
      box-shadow: $box-shadow;
    }

    &.active {
      background: linear-gradient(135deg, #1a3a6c, #2c4272, #3a5998);
      background-size: 200% 200%;
      animation: gradientMove 8s ease infinite;
      color: #ffffff;
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(26, 58, 108, 0.3);
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: none;
        background-color: #ffffff;
        border-color: #e4e8f0;
      }
    }
  }
}
.homepage-pagination-now {
  border: 2px solid $navy-dark !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Media Queries */
@media (max-width: 800px) {
  // Responsive: Giảm tất cả px xuống 1 nửa
  .homepage-productscontainer {
    padding: 20px 10px; // 40px -> 20px, 20px -> 10px
    gap: 15px; // 30px -> 15px
  }

  .loading-container {
    padding: 20px; // 40px -> 20px

    .loading-spinner {
      width: 30px; // 60px -> 30px
      height: 30px; // 60px -> 30px
      border: 3px solid #f3f3f3; // 6px -> 3px
      border-top: 3px solid $navy-dark; // 6px -> 3px
      margin-bottom: 15px; // 30px -> 15px
    }

    p {
      font-size: 10px; // 20px -> 10px
    }
  }

  .no-products {
    padding: 40px; // 80px -> 40px

    i {
      font-size: 32px; // 64px -> 32px
      margin-bottom: 15px; // 30px -> 15px
    }

    p {
      font-size: 10px; // 20px -> 10px
    }
  }

  .category-filter-wrapper {
    padding: 10px 15px; // 20px -> 10px, 30px -> 15px
    margin: 10px 15px; // 20px -> 10px, 30px -> 15px

    .category-filter-inner {
      gap: 6px; // 12px -> 6px

      .filter-label {
        gap: 4px; // 8px -> 4px
        font-size: 7px; // 14px -> 7px

        i {
          font-size: 2px; // 4px -> 2px
        }
      }

      .custom-select-wrapper {
        .category-select-modern {
          min-width: 100px; // 200px -> 100px
          padding: 6px 20px 6px 8px; // 12px -> 6px, 40px -> 20px, 16px -> 8px
          font-size: 7px; // 14px -> 7px
          border-radius: 4px; // 8px -> 4px
        }

        .select-arrow {
          right: 6px; // 12px -> 6px
          font-size: 6px; // 12px -> 6px
        }
      }
    }
  }

  .homepage-dash {
    margin-top: 10px; // 20px -> 10px
  }

  .homepage-pagination {
    margin: 1vh 0; // 2vh -> 1vh
    gap: 5px; // 10px -> 5px

    button {
      border-radius: 4px; // 8px -> 4px
      border: 0.5px solid #e4e8f0; // 1px -> 0.5px
      font-size: 7px; // 14px -> 7px
    }
  }
}

@media (max-width: 768px) {
  .homepage-pagination {
    gap: 4px; // 8px -> 4px

    button {
      width: 17.5px; // 35px -> 17.5px
      height: 17.5px; // 35px -> 17.5px
      font-size: 6.5px; // 13px -> 6.5px
    }
  }
}

@media (max-width: 576px) {
  .homepage-pagination {
    gap: 3px; // 6px -> 3px
    button {
      width: 16px; // 32px -> 16px
      height: 16px; // 32px -> 16px
      font-size: 6px; // 12px -> 6px
    }
  }
}

// THÊM: Category filter styles với màu chính #3a5998, #4caf50, #e53935
.category-filter-wrapper {
  margin: 5px 0 10px 0; // THÊM: Margin cho đẹp hơn
  display: flex;
  justify-content: center;
  padding: 0 20px; // THÊM: Padding cho responsive

  .category-filter-inner {
    display: flex; // THÊM: Flex layout cho label và select
    align-items: center; // THÊM: Căn giữa theo chiều dọc
    gap: 15px; // THÊM: Khoảng cách giữa label và select
    background: linear-gradient(
      135deg,
      $primary-blue 0%,
      darken($primary-blue, 15%) 100%
    ); // THÊM: Gradient với màu chính
    padding: 5px 10px; // THÊM: Padding cho container
    border-radius: 15px; // THÊM: Bo góc đẹp
    box-shadow: 0 8px 25px rgba($primary-blue, 0.3); // THÊM: Shadow với màu chính
    backdrop-filter: blur(10px); // THÊM: Blur effect
    border: 1px solid rgba(255, 255, 255, 0.2); // THÊM: Border trong suốt
    transition: all 0.3s ease; // THÊM: Smooth transition

    &:hover {
      transform: translateY(-2px); // THÊM: Hiệu ứng nâng khi hover
      box-shadow: 0 12px 35px rgba($primary-blue, 0.4); // THÊM: Shadow đậm hơn khi hover
    }

    .filter-label {
      display: flex; // THÊM: Flex cho icon và text
      align-items: center; // THÊM: Căn giữa
      gap: 8px; // THÊM: Khoảng cách icon và text
      color: white; // THÊM: Màu chữ trắng
      font-weight: 600; // THÊM: Font weight đậm
      font-size: 14px; // THÊM: Font size
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); // THÊM: Text shadow

      i {
        font-size: 4px; // THÊM: Size icon
        opacity: 0.9; // THÊM: Độ trong suốt icon
        color: lighten(
          $primary-blue,
          30%
        ); // THÊM: Màu icon sáng hơn từ màu chính
      }
    }

    .custom-select-wrapper {
      position: relative; // THÊM: Relative position cho custom arrow

      .category-select-modern {
        appearance: none; // THÊM: Loại bỏ default arrow
        -webkit-appearance: none; // THÊM: Webkit support
        -moz-appearance: none; // THÊM: Firefox support
        padding: 0px 40px 0px 5px; // THÊM: Padding với space cho custom arrow
        border: 2px solid rgba(255, 255, 255, 0.3); // THÊM: Border trong suốt
        border-radius: 8px; // THÊM: Bo góc đẹp hơn
        font-size: 14px; // THÊM: Font size
        font-weight: 500; // THÊM: Font weight
        background: rgba(255, 255, 255, 0.95); // THÊM: Background trong suốt
        cursor: pointer; // THÊM: Cursor pointer
        min-width: 50px; // THÊM: Min-width
        color: $primary-blue; // THÊM: Màu chữ theo màu chính
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); // THÊM: Smooth transition
        backdrop-filter: blur(10px); // THÊM: Blur effect

        &:focus {
          outline: none; // THÊM: Loại bỏ outline
          border-color: $navy-light; // THÊM: Border màu xanh lá khi focus
          box-shadow: 0 0 0 4px rgba($navy-light, 0.2);
          background: rgba(255, 255, 255, 1); // THÊM: Background khi focus
          transform: translateY(-2px); // THÊM: Hiệu ứng nâng lên
          color: darken($primary-blue, 10%); // THÊM: Màu chữ đậm hơn khi focus
        }

        &:hover {
          border-color: lighten(
            $primary-blue,
            20%
          ); // THÊM: Border sáng hơn khi hover
          background: rgba(255, 255, 255, 1); // THÊM: Background khi hover
          transform: translateY(-1px); // THÊM: Hiệu ứng nâng nhẹ
          box-shadow: 0 4px 15px rgba($primary-blue, 0.2); // THÊM: Shadow khi hover
        }

        option {
          background: white; // THÊM: Background cho option
          color: $primary-blue; // THÊM: Màu chữ option theo màu chính
          padding: 5px; // THÊM: Padding cho option
          font-weight: 500; // THÊM: Font weight cho option

          &:hover {
            background: lighten(
              $primary-blue,
              100%
            ); // THÊM: Background khi hover option
          }

          &:checked {
            background: $navy-light; // THÊM: Background khi selected
            color: white; // THÊM: Màu chữ khi selected
          }
        }
      }

      .select-arrow {
        position: absolute; // THÊM: Absolute position
        right: 15px; // THÊM: Vị trí từ phải
        top: 50%; // THÊM: Căn giữa theo chiều dọc
        transform: translateY(-50%); // THÊM: Transform để căn giữa
        pointer-events: none; // THÊM: Không thể click
        color: $primary-blue; // THÊM: Màu icon theo màu chính
        font-size: 14px; // THÊM: Size icon
        transition: all 0.3s ease; // THÊM: Transition cho animation
        opacity: 0.7; // THÊM: Độ trong suốt
      }

      &:hover .select-arrow {
        transform: translateY(-50%) rotate(180deg); // THÊM: Xoay icon khi hover
        color: $navy-light; // THÊM: Đổi màu icon khi hover
        opacity: 1; // THÊM: Tăng độ đậm khi hover
      }
    }
  }

  // THÊM: Responsive design cho category filter
  @media (max-width: 768px) {
    margin: 3px 0 3px 0; // SỬA: Giảm margin trên mobile
    padding: 0 5px; // SỬA: Giảm padding trên mobile

    .category-filter-inner {
      flex-direction: column; // THÊM: Chuyển thành column trên mobile
      gap: 12px; // SỬA: Giảm gap trên mobile
      padding: 5px 5px; // SỬA: Giảm padding trên mobile
      border-radius: 12px; // SỬA: Giảm border radius trên mobile

      .filter-label {
        font-size: 14px; // SỬA: Giảm font size trên mobile

        i {
          font-size: 14px; // SỬA: Giảm icon size trên mobile
        }
      }

      .custom-select-wrapper {
        .category-select-modern {
          min-width: 200px; // SỬA: Giảm min-width trên mobile
          padding: 10px 40px 10px 18px; // SỬA: Giảm padding trên mobile
          font-size: 14px; // SỬA: Giảm font size trên mobile
        }
      }
    }
  }

  @media (max-width: 480px) {
    .category-filter-inner {
      .custom-select-wrapper {
        .category-select-modern {
          min-width: 180px; // SỬA: Giảm thêm min-width trên mobile nhỏ
          padding: 9px 35px 9px 15px; // SỬA: Giảm thêm padding
          font-size: 14px; // SỬA: Giảm thêm font size
        }

        .select-arrow {
          right: 12px; // SỬA: Điều chỉnh vị trí arrow
          font-size: 12px; // SỬA: Giảm size arrow
        }
      }
    }
  }
}
