.my-orders-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
  min-height: calc(100vh - 100px);
  overflow-y: auto;

  // Ẩn scrollbar
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */

  &::-webkit-scrollbar {
    /* WebKit */
    display: none;
  }
  .my-orders-header {
    margin-bottom: 30px;

    h1 {
      font-size: 28px;
      font-weight: 700;
      color: #0a1f44; // navy-dark
    }
  }

  .no-orders {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    background-color: #ffffff; // white
    border-radius: 6px; // border-radius
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); // box-shadow

    i {
      font-size: 60px;
      color: #888888; // medium-gray
      margin-bottom: 20px;
    }

    p {
      font-size: 18px;
      color: #0a1f44; // navy-dark
      margin-bottom: 30px;
    }

    .shop-now-btn {
      background-color: #1a3f6d; // navy-medium
      color: #ffffff; // white
      padding: 12px 25px;
      border-radius: 6px; // border-radius
      font-size: 16px;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.3s ease; // transition

      &:hover {
        background-color: #0a1f44; // navy-dark
        transform: translateY(-2px);
      }
    }
  }

  .orders-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;

    .order-card {
      background-color: #ffffff; // white
      border-radius: 6px; // border-radius
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); // box-shadow
      overflow: hidden;

      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: #dddddd; // light-gray
        border-bottom: 1px solid #888888; // medium-gray

        .order-info {
          display: flex;
          flex-direction: column;

          .order-id {
            font-weight: 600;
            color: #0a1f44; // navy-dark
            margin-bottom: 5px;
          }

          .order-date {
            font-size: 14px;
            color: #555555; // dark-gray
          }
        }

        .order-status {
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 500;

          &.status-pending {
            background-color: rgba(241, 196, 15, 0.1); // yellow
            color: #f1c40f;
          }
          &.status-processing {
            background-color: rgba(52, 152, 219, 0.1); // blue
            color: #3498db;
          }
          &.status-shipped {
            background-color: rgba(155, 89, 182, 0.1); // purple
            color: #9b59b6;
          }
          &.status-delivered {
            background-color: rgba(46, 204, 113, 0.1); // green
            color: #2ecc71;
          }
          &.status-canceled {
            background-color: rgba(231, 76, 60, 0.1); // red
            color: #e74c3c;
          }
          &.status-unknown {
            background-color: rgba(85, 85, 85, 0.1); // dark-gray
            color: #555555;
          }
        }
      }

      .order-shop {
        display: flex;
        align-items: center;
        padding: 10px 20px;
        background-color: #ffffff; // white
        border-bottom: 1px solid #dddddd; // light-gray

        i {
          color: #1a3f6d; // navy-medium
          margin-right: 10px;
        }

        span {
          font-size: 14px;
          color: #0a1f44; // navy-dark
          font-weight: 500;
        }
      }

      .order-content {
        padding: 20px;
        border-bottom: 1px solid #dddddd; // light-gray

        .order-item-preview {
          display: flex;
          align-items: center;

          .item-image {
            width: 80px;
            height: 80px;
            margin-right: 15px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 4px;
            }
          }

          .item-info {
            flex: 1;

            .item-name {
              font-weight: 500;
              color: #0a1f44; // navy-dark
              margin-bottom: 5px;
            }
            .item-quantity {
              font-size: 14px;
              color: #555555; // dark-gray
            }
          }

          .more-items {
            background-color: #dddddd; // light-gray
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            color: #1a3f6d; // navy-medium
          }
        }

        .no-items {
          text-align: center;
          color: #555555; // dark-gray
          padding: 20px 0;
        }
      }

      .order-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;

        .order-total {
          display: flex;
          align-items: center;
          gap: 10px;

          span {
            font-size: 16px;
            color: #0a1f44; // navy-dark
          }

          .total-price {
            font-weight: 700;
            color: #ff5722; // accent-color
          }
        }

        .view-detail-btn {
          background-color: #3b5f8f; // navy-light
          color: #0a1f44; // navy-dark
          padding: 8px 15px;
          border-radius: 6px; // border-radius
          font-size: 14px;
          text-decoration: none;
          transition: all 0.3s ease; // transition

          &:hover {
            background-color: #1a3f6d; // navy-medium
            color: #ffffff; // white
          }
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid #dddddd; // light-gray
      border-top: 5px solid #1a3f6d; // navy-medium
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    p {
      font-size: 16px;
      color: #0a1f44; // navy-dark
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    padding: 0;
    margin: 30px 0;

    .page-item {
      margin: 0 5px;
      background: none;
      border: 1px solid #888888; // medium-gray
      border-radius: 4px;
      padding: 0;
      cursor: pointer;

      &.active {
        background-color: #1a3f6d; // navy-medium
        border-color: #1a3f6d;

        .page-link {
          color: #ffffff; // white
        }
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        color: #0a1f44; // navy-dark
        text-decoration: none;
        transition: all 0.3s ease; // transition
      }

      &:hover:not(:disabled):not(.active) {
        background-color: #dddddd; // light-gray
        border-color: #3b5f8f; // navy-light
      }
    }
  }
}

// Media query cho màn hình nhỏ hơn 800px
@media (max-width: 800px) {
  .my-orders-container {
    max-width: 1000px;
    padding: 24px 16px;
    min-height: calc(100vh - 80px);

    .my-orders-header {
      margin-bottom: 24px;

      h1 {
        font-size: 22px;
      }
    }

    .no-orders {
      padding: 48px 0;
      border-radius: 5px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

      i {
        font-size: 48px;
        margin-bottom: 16px;
      }

      p {
        font-size: 15px;
        margin-bottom: 24px;
      }

      .shop-now-btn {
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 14px;
      }
    }

    .orders-list {
      gap: 16px;
      margin-bottom: 24px;

      .order-card {
        border-radius: 5px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

        .order-header {
          padding: 12px 16px;

          .order-info {
            .order-id {
              margin-bottom: 4px;
            }

            .order-date {
              font-size: 12px;
            }
          }

          .order-status {
            padding: 5px 10px;
            border-radius: 16px;
            font-size: 12px;
          }
        }

        .order-shop {
          padding: 8px 16px;

          i {
            margin-right: 8px;
          }

          span {
            font-size: 12px;
          }
        }

        .order-content {
          padding: 16px;

          .order-item-preview {
            .item-image {
              width: 64px;
              height: 64px;
              margin-right: 12px;

              img {
                border-radius: 3px;
              }
            }

            .item-info {
              .item-name {
                margin-bottom: 4px;
              }

              .item-quantity {
                font-size: 12px;
              }
            }

            .more-items {
              padding: 4px 8px;
              border-radius: 3px;
              font-size: 12px;
            }
          }

          .no-items {
            padding: 16px 0;
          }
        }

        .order-footer {
          padding: 12px 16px;

          .order-total {
            gap: 8px;

            span {
              font-size: 14px;
            }
          }

          .view-detail-btn {
            padding: 6px 12px;
            border-radius: 5px;
            font-size: 12px;
          }
        }
      }
    }

    .loading-container {
      padding: 48px 0;

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #dddddd;
        border-top: 4px solid #1a3f6d;
        margin-bottom: 16px;
      }

      p {
        font-size: 14px;
      }
    }

    .pagination {
      margin: 24px 0;

      .page-item {
        margin: 0 4px;
        border-radius: 3px;

        .page-link {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}
