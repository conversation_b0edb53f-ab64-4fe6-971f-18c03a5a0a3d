// Variables
$navy-dark: #1a3a6c;
$navy-medium: #2c4272;
$navy-light: #3a5998;
$white: #ffffff;
$light-gray: #f7f9fc;
$medium-gray: #e4e8f0;
$dark-gray: #8492a6;
$accent-color: #ff5a5a;
$blue: #007bff;
$transition: all 0.3s ease;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

// Mixins
@mixin navy-gradient {
  background: linear-gradient(135deg, $navy-dark, $navy-medium, $navy-light);
  background-size: 200% 200%;
  animation: gradientMove 8s ease infinite;
}

@mixin hover-transform {
  transition: $transition;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  }
}

.product-container {
  border: 1px solid $medium-gray;
  padding: 10px;
  border-radius: $border-radius;
  background-color: $white;
  text-align: center;
  height: 500px;
  width: 250px;
  max-height: 500px;
  max-width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  box-shadow: $box-shadow;
  @include hover-transform;

  // Navy gradient strip at top
  &::before {
    content: "";
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    @include navy-gradient;
  }
}

.product-image-container {
  display: flex;
  height: 80px;
  min-height: 80px;
  width: auto;
  // width: 100px;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
  overflow: hidden;
  border-radius: $border-radius;

  .product-image {
    width: auto;
    max-width: 100%;
    height: 90%;
    // object-fit: cover;
    transition: $transition;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.product-name {
  display: inline-block;
  height: fit-content;
  font-size: 12px;
  font-weight: 600;
  color: $navy-dark;
  // overflow: hidden;
  max-width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  // position: relative;
  padding-bottom: 5px;
  margin-bottom: 5px;

  &::after {
    content: "";
    position: relative;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 2px;
    width: 15%;
    @include navy-gradient;
    border-radius: 2px;
  }
}

.product-shop {
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 4px;
  margin: 4px 0;
  padding: 3px 10px;
  background-color: $light-gray;
  border-radius: 20px;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;

  i {
    color: $navy-medium;
    font-size: 10px;
  }

  span {
    color: $navy-dark;
    font-size: 10px;
    font-weight: 500;
  }

  &:hover {
    background-color: $medium-gray;
  }
}

.product-rating-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin: 5px 0;

  .stars-container {
    display: flex;
    align-items: center;
    gap: 3px;

    i {
      color: #ddd;
      font-size: 16px;

      &.filled {
        color: #ffc107;
      }

      &:hover {
        transform: scale(1.2) rotate(5deg);
      }
    }

    .rating-value {
      margin-left: 5px;
      color: $dark-gray;
      font-size: 14px;
    }
  }
}

.product-price {
  font-size: 12px;
  font-weight: 700;
  color: $accent-color;
  margin: 5px 0;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .original-price {
    text-decoration: line-through;
    color: $dark-gray;
    font-size: 10px;
    font-weight: 400;
    margin-bottom: 5px;
  }

  .sale-price {
    color: $accent-color;
    font-size: 12px;
    font-weight: 700;
  }
}

.product-stock {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 10px;
  color: $navy-dark;
  margin: 4px 0;
  font-weight: 500;

  i {
    color: $navy-medium;
    font-size: 10px;
  }

  span {
    color: $navy-dark;
  }
}

.product-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 5px 0;

  .stars-container {
    display: flex;
    gap: 2px;
    margin-bottom: 5px;

    i {
      color: #ddd;
      font-size: 12px;

      &.filled {
        color: #ffc107;
      }
    }
  }

  .rating-value {
    font-size: 10px;
    color: $dark-gray;
  }
}

.product-quantity-container {
  margin: 5px 0;
}

.select-container {
  padding: 4px 12px;
  border: 1px solid $navy-light;
  border-radius: $border-radius;
  background-color: $white;
  color: $navy-dark;
  font-weight: 500;
  transition: $transition;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: $navy-dark;
    box-shadow: 0 0 0 3px rgba($navy-light, 0.15);
  }

  &:hover {
    border-color: $navy-dark;
  }
}
.addToCart-container {
  display: flex;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  align-items: end;
  justify-content: center;
}
.addToCart-button {
  background-color: $navy-medium;
  color: $white;
  border: none;
  height: 30px;
  width: 100%;
  padding: 6px 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  border-radius: $border-radius;
  transition: $transition;
  // margin-top: 5px;
  // position: relative;
  display: inline-block; /* Đảm bảo nút hiển thị đúng kích thước */
  text-align: center; /* Căn giữa text */

  &::before {
    content: "";
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
  }

  &:hover {
    background-color: $navy-dark;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba($navy-dark, 0.3);

    &::before {
      transform: translateX(100%);
    }
  }

  &:active {
    transform: translateY(0);
  }
}
// Styling cho nút thuộc tính sản phẩm
.product-attributes-button {
  background-color: $light-gray;
  height: fit-content;
  color: $navy-dark;
  border: 1px solid $medium-gray;
  border-radius: $border-radius;
  padding: 5px 5px;
  margin: 5px;
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 12ch;
  cursor: pointer;
  transition: $transition;

  &:hover {
    background-color: $navy-light !important;
    color: $white !important;
    border-color: $navy-light !important  ;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba($navy-dark, 0.2);
  }

  &:active {
    transform: translateY(0);
  }

  // Thêm style cho nút được chọn
  &.selected {
    background-color: $white;
    color: $navy-medium;
    border-color: $navy-medium;
    box-shadow: 0 2px 5px rgba($navy-dark, 0.3);
  }
}
.product-etc {
  background-color: $light-gray;
  color: $navy-dark;
  border: 1px solid $medium-gray;
  border-radius: $border-radius;
  padding: 5px 5px;
  margin: 5px;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: $transition;

  &:hover {
    background-color: $navy-light !important;
    color: $white !important;
    border-color: $navy-light !important  ;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba($navy-dark, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
}

// Variant details display
.product-variant-info {
  margin: 5px 0;
  padding: 5px;
  border-radius: $border-radius;
  background-color: $light-gray;
  height: fit-content;

  .variant-details {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .variant-sku {
      display: flex;
      align-items: center;
      gap: 3px;

      .label {
        font-weight: 600;
        color: $navy-dark;
      }

      .value {
        color: $navy-medium;
      }
    }

    .variant-attributes {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .attribute-item {
        background-color: $white;
        padding: 2px 2px;
        border-radius: 4px;
        border: 1px solid $medium-gray;
        display: flex;
        align-items: center;
        gap: 3px;

        .attribute-key {
          font-weight: 600;
          color: $navy-dark;
          font-size: 10px;
        }

        .attribute-value {
          color: $navy-medium;
          font-size: 10px;
        }
      }
    }
  }
}

// Container cho các nút thuộc tính
.product-attributes-container {
  display: flex;
  flex-direction: column;
  margin: 5px 0;

  .attributes-title {
    font-weight: 600;
    color: $navy-dark;
    margin-bottom: 4px;
    text-align: center;
  }

  .attributes-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
  }

  .no-variants {
    color: $dark-gray;
    font-style: italic;
    text-align: center;
    padding: 5px;
  }
}
.product-checkmark {
  width: 12px;
  height: 12px;
  margin-right: 5px;
  transition: $transition;

  &:hover {
    transform: scale(1.2);
  }
}
.product-added {
  color: #4caf50;
  font-weight: 600;
}

// Animations
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Add a badge for new or featured products
.product-badge {
  position: relative;
  top: 10px;
  right: 10px;
  padding: 5px 5px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 15px;
  z-index: 1;

  &.new {
    background-color: $accent-color;
    color: $white;
  }

  &.sale {
    background-color: #4caf50;
    color: $white;
  }

  &.featured {
    @include navy-gradient;
    color: $white;
  }
}

// Add a quick view button that appears on hover
.quick-view-button {
  position: relative;
  // bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba($white, 0.9);
  color: $navy-dark;
  border: none;
  padding: 4px 10px;
  font-size: 10px;
  font-weight: 500;
  border-radius: $border-radius;
  cursor: pointer;
  transition: $transition;
  opacity: 0;

  .product-container:hover & {
    bottom: 20px;
    opacity: 1;
  }

  &:hover {
    background-color: $navy-dark;
    color: $white;
  }
}

// Responsive Design - Mobile & Tablet
@media (max-width: 800px) {
  .product-container {
    border: 0.5px solid $medium-gray;
    padding: 5px;
    border-radius: 4px;
    height: 250px;
    width: 125px;
    max-height: 250px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

    &::before {
      height: 2px;
    }
    &:hover {
      transform: translateY(-2.5px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
  }

  .product-image-container {
    height: 40px;
    min-height: 40px;
    margin-bottom: 2.5px;
    border-radius: 4px;

    .product-image:hover {
      transform: scale(1.025);
    }
  }

  .product-name {
    font-size: 6px;
    padding-bottom: 2.5px;
    margin-bottom: 2.5px;

    &::after {
      height: 1px;
      border-radius: 1px;
    }
  }

  .product-shop {
    gap: 2px;
    margin: 2px 0;
    padding: 1.5px 5px;
    border-radius: 10px;

    i,
    span {
      font-size: 5px;
    }
  }

  .product-rating-container {
    gap: 2.5px;
    margin: 2.5px 0;

    .stars-container {
      gap: 1.5px;

      i {
        font-size: 8px;
        &:hover {
          transform: scale(1.1) rotate(2.5deg);
        }
      }

      .rating-value {
        margin-left: 2.5px;
        font-size: 7px;
      }
    }
  }

  .product-price {
    font-size: 6px;
    margin: 2.5px 0;

    .original-price {
      font-size: 5px;
      margin-bottom: 2.5px;
    }

    .sale-price {
      font-size: 6px;
    }
  }

  .product-stock {
    gap: 4px;
    font-size: 5px;
    margin: 2px 0;

    i {
      font-size: 5px;
    }
  }

  .product-rating {
    margin: 2.5px 0;

    .stars-container {
      gap: 1px;
      margin-bottom: 2.5px;

      i {
        font-size: 6px;
      }
    }

    .rating-value {
      font-size: 5px;
    }
  }

  .product-quantity-container {
    margin: 2.5px 0;
  }

  .select-container {
    padding: 2px 6px;
    border: 0.5px solid $navy-light;
    border-radius: 4px;

    &:focus {
      box-shadow: 0 0 0 1.5px rgba($navy-light, 0.15);
    }
  }

  .addToCart-button {
    height: 15px;
    padding: 3px 10px;
    font-size: 6px;
    border-radius: 4px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2.5px 7.5px rgba($navy-dark, 0.3);
    }
  }

  .product-attributes-button {
    border-radius: 4px;
    padding: 2.5px;
    margin: 2.5px;
    font-size: 5px;
    max-width: 6ch;
    border: 0.5px solid $medium-gray;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 1.5px 4px rgba($navy-dark, 0.2);
    }

    &.selected {
      box-shadow: 0 1px 2.5px rgba($navy-dark, 0.3);
      border: 0.5px solid $navy-medium;
    }
  }

  .product-etc {
    border-radius: 4px;
    padding: 2.5px;
    margin: 2.5px;
    font-size: 5px;
    border: 0.5px solid $medium-gray;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 1.5px 4px rgba($navy-dark, 0.2);
    }
  }

  .product-variant-info {
    margin: 2.5px 0;
    padding: 2.5px;
    border-radius: 4px;

    .variant-details {
      gap: 2px;

      .variant-sku {
        gap: 1.5px;
      }

      .variant-attributes {
        gap: 2px;

        .attribute-item {
          padding: 1px;
          border-radius: 2px;
          border: 0.5px solid $medium-gray;
          gap: 1.5px;

          .attribute-key,
          .attribute-value {
            font-size: 5px;
          }
        }
      }
    }
  }

  .product-attributes-container {
    margin: 2.5px 0;

    .attributes-title {
      margin-bottom: 2px;
    }
    .attributes-buttons {
      gap: 2px;
    }
    .no-variants {
      padding: 2.5px;
    }
  }

  .product-checkmark {
    width: 6px;
    height: 6px;
    margin-right: 2.5px;

    &:hover {
      transform: scale(1.1);
    }
  }

  .product-badge {
    top: 5px;
    right: 5px;
    padding: 2.5px;
    font-size: 6px;
    border-radius: 7.5px;
  }

  .quick-view-button {
    padding: 2px 5px;
    font-size: 5px;
    border-radius: 4px;

    .product-container:hover & {
      bottom: 10px;
    }
  }

  div[style*="height: 48px"] {
    height: 24px !important;
  }
}
