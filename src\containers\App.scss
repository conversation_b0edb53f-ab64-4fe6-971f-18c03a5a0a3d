@import "../styles/common.scss";

.main-container {
  min-height: 100vh; /* <PERSON><PERSON><PERSON> bảo chiều cao tối thiểu bằng chiều cao của viewport */
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .content-container {
    flex: 1; /* Chiếm phần còn lại của container */
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .custom-scrollbar {
      flex: 1; /* Chiếm phần còn lại của content-container */
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      overflow-x: hidden;
      padding-right: 10px; /* Thêm padding để tránh nội dung bị che khi có scrollbar */

      /* Style cho scrollbar */
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(26, 58, 108, 0.5);
        border-radius: 4px;

        &:hover {
          background-color: rgba(26, 58, 108, 0.7);
        }
      }

      /* Style cho Firefox */
      // scrollbar-width: none;
      scrollbar-color: rgba(26, 58, 108, 0.5) rgba(0, 0, 0, 0.05);

      /* Style cho page-content */
      .page-content {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
    }
  }
}
