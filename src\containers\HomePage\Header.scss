// Variables
$navy-dark: #1a3a6c;
$navy-medium: #2c4272;
$navy-light: #3a5998;
$white: #ffffff;
$light-gray: #f7f9fc;
$medium-gray: #e4e8f0;
$dark-gray: #8492a6;
$accent-color: #ff5a5a;
$transition: all 0.3s ease;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

// Mixins
@mixin navy-gradient {
  background: linear-gradient(135deg, $navy-dark, $navy-medium, $navy-light);
  background-size: 200% 200%;
  animation: gradientMove 8s ease infinite;
}

@mixin star-effect {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        1px 1px at 10% 20%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%
      ),
      radial-gradient(
        1.2px 1.2px at 70% 30%,
        rgba(255, 255, 255, 0.5) 50%,
        transparent 100%
      ),
      radial-gradient(
        1px 1px at 30% 70%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%
      ),
      radial-gradient(
        1.3px 1.3px at 80% 60%,
        rgba(255, 255, 255, 0.5) 50%,
        transparent 100%
      ),
      radial-gradient(
        1.1px 1.1px at 50% 40%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%
      );
    pointer-events: none;
    opacity: 0.3;
    animation: twinkle 5s infinite alternate;
  }
}

.header {
  @include navy-gradient;
  @include star-effect;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  padding: 0 20px;
}

.nav-list {
  list-style: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 25px;
  margin: 0;
  padding: 0;
  flex: 1;
}

.nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.home-header {
  width: 35px;
  height: 35px;
  transition: $transition;

  &:hover {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
  }
}

.header-cart {
  width: 30px;
  height: 30px;
  position: fixed;
  top: 15px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: $transition;

  &:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
  }
}

.nav-cart-count {
  position: fixed;
  top: 7px;
  right: 12px;
  background-color: $accent-color;
  color: $white;
  font-size: 12px;
  font-weight: bold;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 75%;
  max-width:  1000px;
}

.searchImage-header {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.7;
  transition: $transition;
  cursor: pointer;
  &:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
}

.searchBar {
  width: 100%;
  height: 38px;
  border: none;
  border-radius: $border-radius;
  outline: none;
  padding: 0 40px 0 15px;
  background-color: rgba(255, 255, 255, 0.8);
  transition: $transition;
  font-size: 14px;
  color: $navy-dark;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  &:focus {
    background-color: $white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  }

  &::placeholder {
    color: $dark-gray;
    opacity: 0.7;
  }
}

.nav-list a {
  text-decoration: none;
  font-size: 15px;
  font-weight: 600;
  color: $white;
  transition: $transition;
  position: relative;
  padding: 5px 0;

  &:hover {
    color: $accent-color;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
  }
}

// Animations
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes twinkle {
  0% {
    opacity: 0.15;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.15;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// Media Queries
@media (max-width: 1200px) {
  .header {
    height: 50px;
  }

  .nav-bar {
    padding: 0 15px;
  }

  .nav-list {
    gap: 15px;
  }

  .home-header {
    width: 30px;
    height: 30px;
  }

  .header-cart {
    width: 28px;
    height: 28px;
    top: 11px;
    right: 15px;
  }

  .nav-cart-count {
    width: 18px;
    height: 18px;
    font-size: 10px;
    top: 5px;
    right: 8px;
  }
}
@media (max-width: 768px) {
  .header {
    height: 50px;
  }

  .nav-bar {
    padding: 0 15px;
  }

  .nav-list {
    gap: 15px;
  }

  .home-header {
    width: 30px;
    height: 30px;
  }

  .header-cart {
    width: 28px;
    height: 28px;
    top: 11px;
    right: 15px;
  }

  .nav-cart-count {
    width: 18px;
    height: 18px;
    font-size: 10px;
    top: 5px;
    right: 8px;
  }
}

@media (max-width: 480px) {
  .header {
    height: 45px;
  }

  .nav-bar {
    padding: 0 10px;
  }

  .nav-list {
    gap: 12px;
  }

  .home-header {
    width: 25px;
    height: 25px;
  }

  .header-cart {
    width: 25px;
    height: 25px;
    top: 10px;
    right: 10px;
  }

  .nav-cart-count {
    width: 16px;
    height: 16px;
    font-size: 9px;
    top: 5px;
    right: 5px;
  }

  .searchImage-header {
    width: 16px;
    height: 16px;
    right: 8px;
  }

  .nav-list a {
    font-size: 13px;
  }
}

@media (max-width: 400px) {
  .header {
    height: 40px;
  }

  .nav-list {
    gap: 8px;
  }

  .home-header {
    width: 22px;
    height: 22px;
  }

  .header-cart {
    width: 22px;
    height: 22px;
    top: 9px;
    right: 8px;
  }

  .nav-cart-count {
    width: 14px;
    height: 14px;
    font-size: 8px;
    top: 4px;
    right: 3px;
  }

  .searchImage-header {
    width: 14px;
    height: 14px;
    right: 7px;
  }

  .nav-list a {
    font-size: 12px;
  }
}
@media (max-width: 800px) {
  // Variables chia đôi
  $border-radius: 4px; // 8px / 2
  $box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08); // 4px / 2, 12px / 2

  // Mixins với px chia đôi
  @mixin star-effect {
    position: relative;

    &::before {
      background-image: radial-gradient(
          0.5px 0.5px at 10% 20%,
          // 1px / 2
          rgba(255, 255, 255, 0.6) 50%,
          transparent 100%
        ),
        radial-gradient(
          0.6px 0.6px at 70% 30%,
          // 1.2px / 2
          rgba(255, 255, 255, 0.5) 50%,
          transparent 100%
        ),
        radial-gradient(
          0.5px 0.5px at 30% 70%,
          // 1px / 2
          rgba(255, 255, 255, 0.6) 50%,
          transparent 100%
        ),
        radial-gradient(
          0.65px 0.65px at 80% 60%,
          // 1.3px / 2
          rgba(255, 255, 255, 0.5) 50%,
          transparent 100%
        ),
        radial-gradient(
          0.55px 0.55px at 50% 40%,
          // 1.1px / 2
          rgba(255, 255, 255, 0.6) 50%,
          transparent 100%
        );
      animation: twinkle 2.5s infinite alternate; // 5s / 2
    }
  }

  .header {
    @include star-effect;
    height: 30px; // 60px / 2
    box-shadow: 0 2px 7.5px rgba(0, 0, 0, 0.15); // 4px / 2, 15px / 2
  }

  .nav-bar {
    max-width: 600px; // 1200px / 2
    padding: 0 10px; // 20px / 2
  }

  .nav-list {
    gap: 12.5px; // 25px / 2
  }

  .nav-item {
    gap: 5px; // 10px / 2
  }

  .home-header {
    width: 17.5px; // 35px / 2
    height: 17.5px; // 35px / 2

    &:hover {
      filter: drop-shadow(0 0 2.5px rgba(255, 255, 255, 0.5)); // 5px / 2
    }
  }

  .header-cart {
    width: 15px; // 30px / 2
    height: 15px; // 30px / 2
    top: 7.5px; // 15px / 2
    right: 10px; // 20px / 2

    &:hover {
      filter: drop-shadow(0 0 2.5px rgba(255, 255, 255, 0.5)); // 5px / 2
    }
  }

  .nav-cart-count {
    top: 3.5px; // 7px / 2
    right: 6px; // 12px / 2
    font-size: 6px; // 12px / 2
    width: 10px; // 20px / 2
    height: 10px; // 20px / 2
    box-shadow: 0 1px 2.5px rgba(0, 0, 0, 0.2); // 2px / 2, 5px / 2
    animation: pulse 1s infinite; // 2s / 2
  }

  .searchImage-header {
    width: 10px; // 20px / 2
    height: 10px; // 20px / 2
    right: 5px; // 10px / 2
  }

  .searchBar {
    height: 19px; // 38px / 2
    border-radius: 4px; // 8px / 2 (from $border-radius)
    padding: 0 20px 0 7.5px; // 40px / 2, 15px / 2
    font-size: 7px; // 14px / 2
    box-shadow: 0 1px 2.5px rgba(0, 0, 0, 0.1); // 2px / 2, 5px / 2

    &:focus {
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15); // 4px / 2, 10px / 2
    }
  }

  .nav-list a {
    font-size: 7.5px; // 15px / 2
    padding: 2.5px 0; // 5px / 2

    &:hover {
      text-shadow: 0 0 2.5px rgba(255, 255, 255, 0.3); // 5px / 2
    }
  }

  // Animations với px chia đôi
  @keyframes gradientMove {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes twinkle {
    0% {
      opacity: 0.15;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.15;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  // Override các media queries hiện tại với px chia đôi
  @media (max-width: 600px) {
    // 1200px / 2
    .header {
      height: 25px; // 50px / 2
    }

    .nav-bar {
      padding: 0 7.5px; // 15px / 2
    }

    .nav-list {
      gap: 7.5px; // 15px / 2
    }

    .home-header {
      width: 15px; // 30px / 2
      height: 15px; // 30px / 2
    }

    .header-cart {
      width: 14px; // 28px / 2
      height: 14px; // 28px / 2
      top: 5.5px; // 11px / 2
      right: 7.5px; // 15px / 2
    }

    .nav-cart-count {
      width: 9px; // 18px / 2
      height: 9px; // 18px / 2
      font-size: 5px; // 10px / 2
      top: 2.5px; // 5px / 2
      right: 4px; // 8px / 2
    }
  }

  @media (max-width: 384px) {
    // 768px / 2
    .header {
      height: 25px; // 50px / 2
    }

    .nav-bar {
      padding: 0 7.5px; // 15px / 2
    }

    .nav-list {
      gap: 7.5px; // 15px / 2
    }

    .home-header {
      width: 15px; // 30px / 2
      height: 15px; // 30px / 2
    }

    .header-cart {
      width: 14px; // 28px / 2
      height: 14px; // 28px / 2
      top: 5.5px; // 11px / 2
      right: 7.5px; // 15px / 2
    }

    .nav-cart-count {
      width: 9px; // 18px / 2
      height: 9px; // 18px / 2
      font-size: 5px; // 10px / 2
      top: 2.5px; // 5px / 2
      right: 4px; // 8px / 2
    }
  }

  @media (max-width: 240px) {
    // 480px / 2
    .header {
      height: 22.5px; // 45px / 2
    }

    .nav-bar {
      padding: 0 5px; // 10px / 2
    }

    .nav-list {
      gap: 6px; // 12px / 2
    }

    .home-header {
      width: 12.5px; // 25px / 2
      height: 12.5px; // 25px / 2
    }

    .header-cart {
      width: 12.5px; // 25px / 2
      height: 12.5px; // 25px / 2
      top: 5px; // 10px / 2
      right: 5px; // 10px / 2
    }

    .nav-cart-count {
      width: 8px; // 16px / 2
      height: 8px; // 16px / 2
      font-size: 4.5px; // 9px / 2
      top: 2.5px; // 5px / 2
      right: 2.5px; // 5px / 2
    }

    .searchImage-header {
      width: 8px; // 16px / 2
      height: 8px; // 16px / 2
      right: 4px; // 8px / 2
    }

    .nav-list a {
      font-size: 6.5px; // 13px / 2
    }
  }

  @media (max-width: 200px) {
    // 400px / 2
    .header {
      height: 20px; // 40px / 2
    }

    .nav-list {
      gap: 4px; // 8px / 2
    }

    .home-header {
      width: 11px; // 22px / 2
      height: 11px; // 22px / 2
    }

    .header-cart {
      width: 11px; // 22px / 2
      height: 11px; // 22px / 2
      top: 4.5px; // 9px / 2
      right: 4px; // 8px / 2
    }

    .nav-cart-count {
      width: 7px; // 14px / 2
      height: 7px; // 14px / 2
      font-size: 4px; // 8px / 2
      top: 2px; // 4px / 2
      right: 1.5px; // 3px / 2
    }

    .searchImage-header {
      width: 7px; // 14px / 2
      height: 7px; // 14px / 2
      right: 3.5px; // 7px / 2
    }

    .nav-list a {
      font-size: 6px; // 12px / 2
    }
  }
}
